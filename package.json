{"name": "startwrite-monorepo", "private": true, "version": "0.0.0", "packageManager": "pnpm@10.12.4", "scripts": {"build": "turbo build", "dev": "turbo dev", "dev:web": "turbo dev --filter=@startwrite/web", "build:web": "turbo build --filter=@startwrite/web", "lint": "turbo lint", "test": "turbo test", "clean": "turbo clean", "db:generate": "turbo db:generate --filter=@startwrite/database", "db:push": "turbo db:push --filter=@startwrite/database", "db:migrate": "turbo db:migrate --filter=@startwrite/database", "type-check": "turbo type-check"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/node": "^20.19.11", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "concurrently": "^9.2.0", "dotenv": "^17.2.1", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "pino-pretty": "^13.1.1", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "turbo": "^2.2.14", "typescript": "^5.9.2", "typescript-eslint": "^8.3.0", "vite": "^7.1.3", "wait-on": "^8.0.4"}, "dependencies": {"@ai-sdk/google": "^2.0.8", "@ai-sdk/react": "^2.0.22", "@prisma/client": "^6.14.0", "@tiptap/extension-character-count": "^3.2.1", "@tiptap/extension-focus": "^3.2.1", "@tiptap/extension-placeholder": "^3.2.1", "@tiptap/react": "^3.2.1", "@tiptap/starter-kit": "^3.2.1", "@types/bcrypt": "^6.0.0", "@types/pg": "^8.15.5", "ai": "^5.0.22", "bcrypt": "^6.0.0", "better-auth": "^1.3.7", "clsx": "^2.1.1", "lucide-react": "^0.344.0", "next": "^15.5.0", "pg": "^8.16.3", "pino": "^9.9.0", "prisma": "^6.14.0", "react": "^19.1.1", "react-dom": "^19.1.1", "tailwind-merge": "^3.3.1", "tsx": "^4.20.4"}}