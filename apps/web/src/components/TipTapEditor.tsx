import React, { useEffect, useState } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import CharacterCount from '@tiptap/extension-character-count';
import Focus from '@tiptap/extension-focus';
import { <PERSON>rkles, ArrowRight, Lightbulb, X, Loader2 } from 'lucide-react';

interface TipTapEditorProps {
  placeholder?: string;
  onContentChange?: (content: string) => void;
  initialContent?: string;
  showAIPrompts?: boolean;
}

export function TipTapEditor({ 
  placeholder = "Start writing...", 
  onContentChange,
  initialContent = "",
  showAIPrompts = true 
}: TipTapEditorProps) {
  const [showAIPanel, setShowAIPanel] = useState(false);
  const [aiSuggestion, setAiSuggestion] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);

  const editor = useEditor({
    extensions: [
      StarterKit,
      Placeholder.configure({
        placeholder,
      }),
      CharacterCount,
      Focus.configure({
        className: 'has-focus',
        mode: 'all',
      }),
    ],
    content: initialContent,
    onUpdate: ({ editor }) => {
      const content = editor.getHTML();
      onContentChange?.(content);
    },
    editorProps: {
      attributes: {
        class: 'prose prose-slate max-w-none focus:outline-none min-h-[400px] p-4 prose-headings:text-slate-900 prose-p:text-slate-700 prose-strong:text-slate-900 prose-em:text-slate-700',
      },
      handleKeyDown: (view, event) => {
        // Handle Shift+Enter for AI continue writing
        if (event.key === 'Enter' && event.shiftKey) {
          event.preventDefault();
          handleContinueWriting();
          return true;
        }
        return false;
      },
    },
  });

  const handleContinueWriting = async () => {
    if (!editor || isGenerating) return;

    const currentContent = editor.getText();
    if (!currentContent.trim()) {
      setAiSuggestion("Start writing something first, then press Shift+Enter for AI assistance!");
      setShowAIPanel(true);
      return;
    }

    setIsGenerating(true);
    setShowAIPanel(true);
    setAiSuggestion('');

    try {
      const response = await fetch('/api/continue', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text: currentContent }),
      });

      if (!response.ok) {
        throw new Error('Failed to get AI suggestion');
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      let suggestion = '';
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = new TextDecoder().decode(value);
        suggestion += chunk;
        setAiSuggestion(suggestion);
      }
    } catch (error) {
      console.error('Error getting AI suggestion:', error);
      setAiSuggestion('Sorry, I encountered an error while generating suggestions. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const insertSuggestionAsText = () => {
    if (!editor || !aiSuggestion) return;
    
    // Insert the suggestion as a new paragraph
    editor.chain().focus().insertContent(`<p>${aiSuggestion}</p>`).run();
    setShowAIPanel(false);
    setAiSuggestion('');
  };

  const closeAIPanel = () => {
    setShowAIPanel(false);
    setAiSuggestion('');
  };

  useEffect(() => {
    if (editor && initialContent !== editor.getHTML()) {
      editor.commands.setContent(initialContent);
    }
  }, [editor, initialContent]);

  if (!editor) {
    return (
      <div className="border border-slate-200 rounded-lg p-4 min-h-[400px] flex items-center justify-center">
        <Loader2 className="animate-spin text-slate-400" size={24} />
      </div>
    );
  }

  return (
    <div className="relative">
      <div className="border border-slate-200 rounded-lg overflow-hidden focus-within:ring-2 focus-within:ring-indigo-500 focus-within:border-transparent">
        <EditorContent editor={editor} />
        
        {/* Character count and AI hint */}
        <div className="flex items-center justify-between px-4 py-2 bg-slate-50 border-t border-slate-200 text-sm text-slate-500">
          <div className="flex items-center space-x-4">
            <span>{editor.storage.characterCount.characters()} characters</span>
            <span>{editor.storage.characterCount.words()} words</span>
          </div>
          {showAIPrompts && (
            <div className="flex items-center space-x-2">
              <Sparkles size={14} className="text-indigo-500" />
              <span>Press Shift+Enter for AI writing assistance</span>
            </div>
          )}
        </div>
      </div>

      {/* AI Suggestion Panel */}
      {showAIPanel && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-slate-200 rounded-lg shadow-lg z-10">
          <div className="flex items-center justify-between p-4 border-b border-slate-200">
            <div className="flex items-center space-x-2">
              <Sparkles className="text-indigo-500" size={18} />
              <h3 className="font-medium text-slate-900">AI Writing Assistant</h3>
            </div>
            <button
              onClick={closeAIPanel}
              className="text-slate-400 hover:text-slate-600 transition-colors"
            >
              <X size={18} />
            </button>
          </div>
          
          <div className="p-4">
            {isGenerating ? (
              <div className="flex items-center space-x-2 text-slate-600">
                <Loader2 className="animate-spin" size={16} />
                <span>Generating writing suggestions...</span>
              </div>
            ) : aiSuggestion ? (
              <div className="space-y-4">
                <div className="bg-slate-50 rounded-lg p-3">
                  <div className="flex items-start space-x-2">
                    <Lightbulb className="text-amber-500 mt-0.5" size={16} />
                    <p className="text-slate-700 text-sm leading-relaxed">{aiSuggestion}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={insertSuggestionAsText}
                    className="inline-flex items-center space-x-2 px-3 py-2 bg-indigo-600 text-white text-sm rounded-lg hover:bg-indigo-700 transition-colors"
                  >
                    <ArrowRight size={14} />
                    <span>Use this suggestion</span>
                  </button>
                  <button
                    onClick={handleContinueWriting}
                    className="inline-flex items-center space-x-2 px-3 py-2 border border-slate-200 text-slate-700 text-sm rounded-lg hover:bg-slate-50 transition-colors"
                  >
                    <Sparkles size={14} />
                    <span>Get another suggestion</span>
                  </button>
                </div>
              </div>
            ) : null}
          </div>
        </div>
      )}
    </div>
  );
}
