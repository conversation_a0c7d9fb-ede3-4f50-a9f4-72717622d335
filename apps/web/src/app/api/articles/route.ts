import { NextRequest } from 'next/server'
import { requireAuth } from '@/lib/auth-middleware'
import { Article } from '@/lib/types'
import { getUserArticles, addArticle } from '@/lib/articles-store'

export async function GET(request: NextRequest) {
  const user = await requireAuth(request);
  if (user instanceof Response) {
    return user; // Return the error response
  }

  const userArticles = getUserArticles(user.id);
  return Response.json(userArticles);
}

export async function POST(request: NextRequest) {
  const user = await requireAuth(request);
  if (user instanceof Response) {
    return user; // Return the error response
  }

  try {
    const { title, content } = await request.json();
    const newArticle: Article = {
      id: crypto.randomUUID(),
      title,
      content,
      userId: user.id,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    addArticle(newArticle);
    return Response.json(newArticle, { status: 201 });
  } catch (error) {
    console.error('Error creating article:', error);
    return Response.json(
      { error: 'Failed to create article' },
      { status: 500 }
    );
  }
}
