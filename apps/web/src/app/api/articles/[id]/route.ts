import { NextRequest } from 'next/server'
import { requireAuth } from '@/lib/auth-middleware'
import { getArticleById, updateArticle, deleteArticle } from '@/lib/articles-store'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const user = await requireAuth(request);
  if (user instanceof Response) {
    return user;
  }

  const { id } = await params;
  const article = getArticleById(id);

  if (!article || article.userId !== user.id) {
    return Response.json({ error: 'Article not found' }, { status: 404 });
  }
  
  return Response.json(article);
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const user = await requireAuth(request);
  if (user instanceof Response) {
    return user;
  }

  try {
    const { id } = await params;
    const { title, content } = await request.json();
    const article = getArticleById(id);

    if (!article || article.userId !== user.id) {
      return Response.json({ error: 'Article not found' }, { status: 404 });
    }

    const updatedArticle = updateArticle(id, {
      title: title ?? article.title,
      content: content ?? article.content,
      updatedAt: new Date(),
    });

    return Response.json(updatedArticle);
  } catch (error) {
    console.error('Error updating article:', error);
    return Response.json(
      { error: 'Failed to update article' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const user = await requireAuth(request);
  if (user instanceof Response) {
    return user;
  }

  const { id } = await params;
  const article = getArticleById(id);

  if (!article || article.userId !== user.id) {
    return Response.json({ error: 'Article not found' }, { status: 404 });
  }

  const deleted = deleteArticle(id);

  if (!deleted) {
    return Response.json({ error: 'Failed to delete article' }, { status: 500 });
  }

  return Response.json({ message: 'Article deleted' });
}
