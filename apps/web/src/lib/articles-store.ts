import { Article } from './types'

// Temporary in-memory storage (same as Hono implementation)
// In production, this should use a database
const articles: Article[] = [];

export function getArticles(): Article[] {
  return articles;
}

export function addArticle(article: Article): void {
  articles.push(article);
}

export function updateArticle(id: string, updates: Partial<Article>): Article | null {
  const article = articles.find(a => a.id === id);
  if (!article) return null;
  
  Object.assign(article, updates);
  return article;
}

export function deleteArticle(id: string): boolean {
  const index = articles.findIndex(a => a.id === id);
  if (index === -1) return false;
  
  articles.splice(index, 1);
  return true;
}

export function getUserArticles(userId: string): Article[] {
  return articles.filter(a => a.userId === userId);
}

export function getArticleById(id: string): Article | undefined {
  return articles.find(a => a.id === id);
}
