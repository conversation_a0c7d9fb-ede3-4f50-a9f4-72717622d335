{"name": "@startwrite/ui", "version": "0.1.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"lint": "eslint .", "type-check": "tsc --noEmit"}, "dependencies": {"clsx": "^2.1.1", "lucide-react": "^0.344.0", "react": "^19.1.1", "react-dom": "^19.1.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "typescript": "^5.5.3"}, "peerDependencies": {"react": "^19.1.1", "react-dom": "^19.1.1"}}